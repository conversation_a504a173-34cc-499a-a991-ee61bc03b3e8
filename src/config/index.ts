// 配置文件
interface ApiConfig {
  baseUrl: string;
  projectsEndpoint: string;
  sessionEndpoint: string;
  chatEndpoint: string;
}

interface WidgetConfig {
  defaultColor: string;
  defaultAssistantName: string;
  defaultWelcomeMessage: string;
  defaultSuggestedQuestions: string[];
}

// Widget 默认配置
const WIDGET_DEFAULTS: WidgetConfig = {
  defaultColor: "#3B82F6",
  defaultAssistantName: "AI助手",
  defaultWelcomeMessage: "您好！我是您的AI助手，有什么可以帮助您的吗？",
  defaultSuggestedQuestions: [
    "我想了解产品信息",
    "如何联系客服",
    "有什么优惠活动吗",
  ],
};

// 创建API配置的辅助函数
const createApiConfig = (baseUrl: string): ApiConfig => ({
  baseUrl,
  projectsEndpoint: `${baseUrl}/v1/projects`,
  sessionEndpoint: `${baseUrl}/v1/sessions`,
  chatEndpoint: `${baseUrl}/v1/chat`,
});

// 根据环境变量或构建模式确定配置
const isDevelopment = import.meta.env.DEV;
// Widget专用配置（用于编译后的widget.js）
export const getWidgetConfig = () => {
  // 直接使用固定的API地址，简化配置
  /*const baseUrl = isDevelopment
    ? "http://127.0.0.1:8081"
    : "https://www.yapyapbot.com";*/
  const baseUrl = "https://www.yapyapbot.com";
  return {
    api: createApiConfig(baseUrl),
    widget: WIDGET_DEFAULTS,
  };
};

export const supportMail = "<EMAIL>";

// 主应用聊天API配置
export const getChatApiUrl = () => {
  const protocol = window.location.protocol;
  const host = window.location.host;
  return `${protocol}//${host}/v1/chat`;
};

// 会话API配置
export const getSessionApiUrl = () => {
  const protocol = window.location.protocol;
  const host = window.location.host;
  return `${protocol}//${host}/v1/sessions`;
};
