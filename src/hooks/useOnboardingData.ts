import { FormConfig } from "@/pages/FormBuilder/types/form-types";
import { createProject, CreateProjectResponse } from "@/services/api";
import { formApi } from "@/services/api/form";
import { useState } from "react";
import { v4 as uuidv4 } from "uuid";

interface OnboardingData {
  websiteUrl: string;
  brandName: string;
  brandColor: string;
  logo: string | null;
  welcomeMessage: string;
  suggestedQuestions: string[];
  suggestedQuestionsEnabled: boolean;
}

const STORAGE_KEY = "onboarding_data";
const STORAGE_FORM_KEY = "onboarding_form_data";

const defaultData: OnboardingData = {
  websiteUrl: "",
  brandName: "YapYap",
  brandColor: "#9067bc",
  logo: null,
  welcomeMessage:
    "Welcome! 👋\n\nI'm YapYapBot, here to assist with any questions you have. How can I help you today?",
  suggestedQuestions: [
    "No questions.",
    "How are you?",
    "I have another question",
  ],
  suggestedQuestionsEnabled: true,
};

export const useOnboardingData = () => {
  const [formData, setFormData] = useState<FormConfig>(() => {
    // 从 localStorage 初始化 formData
    const stored = localStorage.getItem(STORAGE_FORM_KEY);
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        return parsed;
      } catch (error) {
        console.error("Failed to parse onboarding form data:", error);
      }
    }
    return undefined;
  });

  const updateFormData = (updates: Partial<FormConfig>) => {
    setFormData((prev) => {
      const newData = { ...prev, ...updates };
      // 确保在数据更新后立即存储到 localStorage
      try {
        localStorage.setItem(STORAGE_FORM_KEY, JSON.stringify(newData));
        console.log("Data saved to localStorage:", newData);
      } catch (error) {
        console.error("Failed to save to localStorage:", error);
      }
      return newData;
    });
  };

  const [data, setData] = useState<OnboardingData>(() => {
    // 在初始化时就从 localStorage 读取数据
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.error("Failed to parse onboarding data:", error);
      }
    }
    return defaultData;
  });

  const updateData = (updates: Partial<OnboardingData>) => {
    setData((prev) => {
      const newData = { ...prev, ...updates };
      // 确保在数据更新后立即存储到 localStorage
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(newData));
        console.log("Data saved to localStorage:", newData);
      } catch (error) {
        console.error("Failed to save to localStorage:", error);
      }
      return newData;
    });
  };

  const createProjectAndForm = async (
    projectId?: string
  ): Promise<CreateProjectResponse> => {
    const pid = projectId ? projectId : uuidv4();
    const projectData = {
      projectId: pid,
      name: data.brandName,
      website: data.websiteUrl,
      settings: {
        logo: data.logo || "",
        name: data.brandName,
        color: data.brandColor,
        welcomeMsg: data.welcomeMessage,
        suggestedEnable: data.suggestedQuestionsEnabled,
        suggestedQuestions: data.suggestedQuestions,
      },
    };

    const response = await createProject(projectData);

    if (formData) {
      formData.active = true;
      await formApi.saveForm(pid, formData);
    }
    // 清除onboarding数据
    clearData();

    return response;
  };

  // 可选：添加一个清除数据的方法
  const clearData = () => {
    localStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(STORAGE_FORM_KEY);
    setData(defaultData);
  };

  return {
    data,
    formData,
    updateFormData,
    updateData,
    clearData,
    createProjectAndForm,
  };
};
